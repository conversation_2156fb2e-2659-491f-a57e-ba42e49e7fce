// 基础服务类，提供通用功能
export class BaseService {
    constructor(context) {
      this.context = context // Vue组件实例
      this.loading = false
    }
  
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
      this.context.loading = loading
      this.context.gridOptions.loading = loading
    }
  
    // 统一错误处理
    handleError(error) {
      console.error('服务错误:', error)
      this.context.$message.error(error.message || '操作失败')
      this.setLoading(false)
    }
  
    // 成功提示
    showSuccess(message = '操作成功') {
      this.context.$message.success(this.context.$t ? this.context.$t('public.success') : message)
    }
  
    // 同步事件发射器
    emitSyncEvent(event, data) {
      return new Promise((resolve) => {
        if (this.context.$listeners[event]) {
          this.context.$emit(event, data, resolve)
        } else {
          resolve(data)
        }
      })
    }
  
    // 请求失败处理
    requestFailed(error) {
      if (this.context.requestFailed) {
        this.context.requestFailed(error)
      } else {
        this.handleError(error)
      }
    }
} 