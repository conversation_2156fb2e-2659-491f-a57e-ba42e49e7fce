<template>
  <div class="scheme-dialog-container">
    <el-dialog
      class="JustMake-dialog"
      title="查询方案管理"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :before-close="handleClose"
      width="1200px"
    >
      <!-- 另存为对话框 -->
      <el-dialog
        title="另存为"
        class="JustMake-dialog"
        :modal-append-to-body="false"
        :append-to-body="true"
        :visible.sync="saveAsDialogVisible"
        :close-on-click-modal="false"
        :destroy-on-close="true"
        width="400px"
        top="35vh"
        @close="handleSaveAsCancel"
      >
        <el-form
          ref="saveAsForm"
          :show-message="false"
          :hide-required-asterisk="true"
          :model="saveAsForm"
          :rules="saveAsFormRules"
          label-width="auto"
          label-position="left"
        >
          <el-form-item :show-message="false" label="方案名称" prop="name" style="margin-bottom: 0;">
            <el-input
              v-model="saveAsForm.name"
              placeholder="请输入方案名称"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item :show-message="false" label="方案类型" prop="type" style="margin-bottom: 0;">
            <el-select
              v-model="saveAsForm.type"
              placeholder="请选择方案类型"
              style="width: 100%"
            >
              <el-option
                v-for="(label, value) in schemeTypeMap"
                :key="value"
                :label="label"
                :value="Number(value)"
                :disabled="Number(value) === 0"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
        <el-button @click="handleSaveAsCancel">取消</el-button>
        <el-button type="primary" @click="handleSaveAsConfirm">确定</el-button>
      </span>
      </el-dialog>

      <div class="scheme-dialog-content">
        <!-- 上部分：工具栏 -->
        <div class="toolbar">
          <div class="toolbar-btn">
            <el-button type="text" @click="handleSave" :disabled="!canSaveSystemScheme">
              <svg-icon icon-class="icon-save" />
              保存
            </el-button>
            <el-button type="text" @click="handleSaveAs" >
              <svg-icon icon-class="icon-copy" />
              另存为
            </el-button>
            <el-button  type="text" @click="handleDelete" :disabled="isSystemScheme">
              <svg-icon icon-class="icon-delete" />
              删除
            </el-button>
            <el-button  type="text" @click="handleDelete" :disabled="true">
              <svg-icon icon-class="icon-export" />
              导出
            </el-button>
            <el-button  type="text" @click="handleDelete" :disabled="true">
              <svg-icon icon-class="icon-import" />
              导入
            </el-button>
          </div>
        </div>
        
        <!-- 下部分：操作区域 -->
        <div class="operation-section">
          <!-- 左侧：方案列表 -->
          <div class="scheme-list-section">
            <el-input
              placeholder="搜索方案"
              v-model="searchKeyword"
              prefix-icon="el-icon-search"
              clearable
              @change="handleSearch"
              class="scheme-search"
            ></el-input>
            <div class="scheme-list">
              <vxe-grid ref="listGridRef" class="vxe-grid-common" v-bind="listGridOptions"
                        @cell-click="handleCellClick">
                <template #switch1_default="{ row }">
                  <div class="switch-cell">
                    <el-switch
                      v-model="row.is_show"
                      active-value="T"
                      inactive-value="F"
                      size="mini"
                      active-color="#08b68b"
                      inactive-color="#dcdfe6"
                    ></el-switch>
                  </div>
                </template>

                <template #name_default="{ row }">
                  <span
                    class="scheme-name"
                    @click="handleSchemeNameClick(row)"
                  >
                    {{ row.name }}
                    <span v-if="enableDevModel && devModeEnabled && row.type == 0" class="dev-mode-indicator">⚙</span>
                  </span>
                </template>

                <template #type_default="{ row }">
                  <span class="scheme-type">
                    {{ getSchemeTypeName(row.type) }}
                  </span>
                </template>
              </vxe-grid>
            </div>
          </div>
          
          <!-- 右侧：方案详情 -->
          <div class="scheme-detail-section">
            <div class="scheme-tabs" v-if="currentScheme" :key="currentScheme.id">
              <el-tabs v-model="activeTab" :key="`tabs-${currentScheme.id}`">
                <el-tab-pane label="方案条件" name="conditions">
                  <div class="tab-content" v-if="activeTab === 'conditions'" >
                    <!-- 方案条件内容：预先定义好的固定条件，每次查询都会携带-->
                    <scheme-conditions
                      :key="`conditions-${currentScheme.id}`"
                      :condition-groups="searchContent"
                      :field-config="currentFieldConfig"
                      @change="handleSchemeConditionChange"
                      @delete-group="handleSchemeConditionGroupDelete"
                      @add-group="handleSchemeConditionGroupAdd"
                    ></scheme-conditions>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="快速过滤" name="filters">
                  <div class="tab-content" v-if="activeTab === 'filters'" >
                    <!-- 快速过滤内容：提供给用户自定义查询的条件 -->
                    <scheme-filters
                      :key="`filters-${currentScheme.id}`"
                      :conditions="condContent"
                      :field-config="currentFieldConfig"
                      @change="handleTempConditionChange"
                      @delete="handleTempConditionDelete"
                      @add="handleTempAddCondition"
                      @del-all="handleTempDelAllCondition"
                    ></scheme-filters>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="栏位设置" name="columns">
                  <div class="tab-content" v-if="activeTab === 'columns'" :key="`columns-${currentScheme.id}`">
                    <!-- 栏位设置内容 -->
                    <div class="column-settings">
                      <!-- 搜索栏和配置按钮 -->
                      <div class="column-search-section">
                        <div class="column-search-row">
                          <el-input
                            placeholder="搜索栏位代号,栏位名称"
                            v-model="columnSearchKeyword"
                            prefix-icon="el-icon-search"
                            clearable
                            @input="handleColumnSearch"
                            class="column-search-input"
                          ></el-input>
                          <el-button
                            type="primary"
                            size="small"
                            @click="handleConfigBaseFields"
                            class="config-base-fields-btn"
                          >
                            配置基础资料栏位
                          </el-button>
                        </div>
                      </div>

                      <!-- 栏位表格 -->
                      <div class="column-table-section">
                        <div class="column-table-container">
                          <vxe-grid
                            ref="columnGridRef"
                            :key="`column-grid-${currentScheme.id}`"
                            class="vxe-grid-common"
                            v-bind="columnGridOptions"
                            @cell-click="handleColumnCellClick">

                            <!-- 显示开关：启用则在表身显示，可以在右键菜单-栏位设置中调整 -->
                            <template #display_switch_default="{ row }">
                              <div class="switch-cell">
                                <el-switch
                                  v-model="row.is_show"
                                  active-value="T"
                                  inactive-value="F"
                                  size="mini"
                                  active-color="#08b68b"
                                  inactive-color="#dcdfe6"
                                  @change="handleColumnShowChange(row)"
                                ></el-switch>
                              </div>
                            </template>

                            <!-- 禁用开关：启用后，在右键菜单-栏位设置中不显示 -->
                            <template #disable_switch_default="{ row }">
                              <div class="switch-cell">
                                <el-switch
                                  v-model="row.used"
                                  active-value="T"
                                  inactive-value="F"
                                  size="mini"
                                  active-color="#08b68b"
                                  inactive-color="#dcdfe6"
                                  @change="handleColumnDisableChange(row)"
                                ></el-switch>
                              </div>
                            </template>

                            <!-- 栏位别名可编辑输入 -->
                            <template #alias_input_default="{ row }">
                              <el-input
                                v-model="row.rem_gb"
                                size="mini"
                                placeholder="请输入栏位别名"
                                @blur="handleColumnAliasChange(row)"
                              ></el-input>
                            </template>

                            <!-- 自定义宽度可编辑输入：定义栏位宽度，影响方案视图的列宽度；用户在表头拖动则是影响用户视图 -->
                            <template #width_input_default="{ row }">
                              <el-input
                                v-model="row.size"
                                size="mini"
                                placeholder="请输入宽度"
                                @blur="handleColumnWidthChange(row)"
                              ></el-input>
                            </template>

                          </vxe-grid>
                          <!-- 栏位排序按钮 -->
                          <div class="column-sort-buttons">
                            <el-button class="sort-button" size="small" icon="el-icon-top" @click="moveColumnTop" title="移动到顶部"></el-button>
                            <el-button class="sort-button" size="small" icon="el-icon-arrow-up" @click="moveColumnUp" title="向上移动"></el-button>
                            <el-button class="sort-button" size="small" icon="el-icon-arrow-down" @click="moveColumnDown" title="向下移动"></el-button>
                            <el-button class="sort-button" size="small" icon="el-icon-bottom" @click="moveColumnBottom" title="移动到底部"></el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="排序设定" name="sorting">
                  <div class="tab-content" v-if="activeTab === 'sorting'" >
                    <!-- 排序设定内容 -->
                    <div class="sort-settings">
                      <!-- 搜索栏 -->
                      <div class="sort-search-section">
                        <el-input
                          placeholder="搜索栏位名称"
                          v-model="sortSearchKeyword"
                          prefix-icon="el-icon-search"
                          clearable
                          @input="handleSortSearch"
                          class="sort-search-input"
                        ></el-input>
                      </div>

                      <!-- 排序表格 -->
                      <div class="sort-table-section">
                        <vxe-grid
                          ref="sortGridRef"
                          :key="`sort-grid-${currentScheme.id}`"
                          class="vxe-grid-common"
                          v-bind="sortGridOptions"
                          @cell-click="handleSortCellClick">

                          <!-- 排序方式下拉框 -->
                          <template #sort_type_default="{ row }">
                            <el-select
                              v-model="row.sort"
                              size="mini"
                              placeholder="请选择排序方式"
                              @change="handleSortTypeChange(row)"
                            >
                              <el-option label="升序" value="asc"></el-option>
                              <el-option label="降序" value="desc"></el-option>
                            </el-select>
                          </template>

                        </vxe-grid>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
            
            <div class="no-scheme-selected" v-else>
              <div class="empty-state">
                <i class="el-icon-document"></i>
                <p>请选择或创建一个方案</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave" :disabled="!canSaveSystemScheme">保存并应用</el-button>
        <el-button type="primary" @click="handleApply">应用</el-button>
      </span>
    </el-dialog>

    <!-- 配置基础资料栏位对话框 -->
    <base-field-config-dialog
      ref="baseFieldConfigDialog"
      :FUNID="FUNID"
      :MODULEValue="MODULEValue"
      :existing-columns="columnContent"
      @confirm="handleBaseFieldConfigConfirm"
    ></base-field-config-dialog>
  </div>
</template>

<script>
import { getSchemeSet, saveScheme, deleteScheme, saveAsScheme, setCurScheme } from '@/api/admin/base'
import SchemeFilters from '@/components/amtxts/common/SchemeManager/SchemeFilters.vue'
import SchemeConditions from '@/components/amtxts/common/SchemeManager/SchemeConditions.vue'
import BaseFieldConfigDialog from '@/components/amtxts/common/SchemeManager/BaseFieldConfigDialog.vue'
import { getDefaultOperator, isDynamicDateOperator } from '@/components/amtxts/common/SearchForm/mixins/operators'
import { validateCondition } from '@/components/amtxts/common/SearchForm/utils/valueValidator'
import { SchemeDataUtils } from '@/components/amtxts/common/SearchForm/utils/DataUtils'


export default {
  name: 'SchemeDialog',
  components: { SchemeFilters, SchemeConditions, BaseFieldConfigDialog },
  props: {
    fieldConfig: {
      type: Array,
      default: () => []
    },
    currentView: {
      type: Object,
      default: () => {}
    },
    FUNID:{
      type: String,
      required: true
    },
    MODULEValue:{
      type: String,
      required: true
    },
    // 开发后门控制参数（开启后，连续点击5次方案名称开启开发模式，允许保存系统方案）
    enableDevModel: {
      type: Boolean,
      default: false // 默认关闭后门功能
    }
  },
  data() {
    return {
      dialogVisible: false,
      refresh: false,
      searchKeyword: '',
      activeTab: 'conditions',
      currentScheme: null,
      schemeData: [], // 方案设定数据集合
      condContent:[], // 快速过滤
      columnContent:[], // 栏位设置
      sortContent:[],// 排序设定
      searchContent:[], // 方案条件
      columnSearchKeyword: '', // 栏位搜索关键词
      originalColumnContent: [], // 原始栏位数据，用于搜索过滤
      currentColumnRowIndex: null, // 当前选中的栏位行索引
      originalSchemeData: [],  // 原始方案数据，用于搜索过滤
      sortSearchKeyword: '', // 排序搜索关键词
      originalSortContent: [], // 原始排序数据，用于搜索过滤
      internalFieldConfig: [], // 内部字段配置副本，用于同步基础资料栏位变更
      // 开发后门相关
      devModeEnabled: false, // 开发模式标识
      devModeClickCount: 0, // 开发模式点击计数
      devModeTimer: null, // 开发模式计时器
      schemeTypeMap: {
        0: '系统方案',
        1: '公共方案',
        2: '我的方案'
      },
      // 另存为对话框相关数据
      saveAsDialogVisible: false,
      saveAsForm: {
        name: '',
        type: 2 // 默认为我的方案
      },
      saveAsFormRules: {
        name: [
          { required: true, message: '请输入方案名称', trigger: 'blur' },
          { min: 1, max: 50, message: '方案名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择方案类型', trigger: 'change' }
        ]
      },
      listGridOptions:{
        border: true,
        stripe: true,
        showOverflow: true,
        showHeaderOverflow: true,
        rowConfig: { isCurrent: true, },
        height: '100%',
        loading: false,
        headerAlign: 'center',
        columns: [
          {
            field: 'is_show',
            width: 80,
            title: '显示',
            slots: {default: 'switch1_default'}
          },
          {
            field: 'name',
            title: '方案名称',
            slots: { default: 'name_default' }
          },
          {
            field: 'type',
            title: '类型',
            width: 80,
            slots: { default: 'type_default' }
          }
        ],
        data: [ ],
      },
      columnGridOptions: {
        border: true,
        stripe: true,
        showOverflow: true,
        showHeaderOverflow: true,
        rowConfig: { isCurrent: true, },
        height: '100%',
        loading: false,
        headerAlign: 'center',
        columns: [
          { type: 'seq', width: 60, title: '项次', align: 'center' },
          {
            field: 'is_show',
            width: 80,
            title: '显示',
            slots: { default: 'display_switch_default' }
          },
          {
            field: 'used',
            width: 80,
            title: '禁用',
            slots: { default: 'disable_switch_default' }
          },
          { field: 'field', title: '栏位代号', width: 150,  },
          { field: 'title', title: '栏位名称',  },
          {
            field: 'rem_gb',
            title: '栏位别名',
            width: 150,
            slots: { default: 'alias_input_default' }
          },
          {
            field: 'size',
            title: '自定义宽度',
            width: 120,
            slots: { default: 'width_input_default' }
          }
        ],
        data: []
      },
      sortGridOptions: {
        border: true,
        stripe: true,
        showOverflow: true,
        showHeaderOverflow: true,
        rowConfig: { isCurrent: true, },
        height: '100%',
        loading: false,
        headerAlign: 'center',
        columns: [
          { type: 'seq', width: 60, title: '项次', align: 'center' },
          { field: 'title', title: '栏位名称', minWidth: 150 },
          {
            field: 'sort',
            title: '排序方式',
            width: 150,
            slots: { default: 'sort_type_default' }
          }
        ],
        data: []
      }
    }
  },
  beforeDestroy() {
    // 组件销毁前清理数据
    this.resetDialogData()
    // 清理开发模式计时器
    if (this.devModeTimer) {
      clearTimeout(this.devModeTimer)
      this.devModeTimer = null
    }
  },
  destroyed() {
    // 确保所有引用都被清理
    this.currentScheme = null
    this.schemeData = []
    this.originalSchemeData = []
    this.originalColumnContent = []
  },
  computed: {
    // 判断当前方案是否为系统方案
    isSystemScheme() {
      return this.currentScheme && this.currentScheme.type == 0
    },

    // 判断是否允许保存系统方案（开发后门）
    canSaveSystemScheme() {
      // 如果没有启用开发后门功能，则按原逻辑处理
      if (!this.enableDevModel) {
        return !this.isSystemScheme
      }
      // 启用开发后门功能时，考虑开发模式状态
      return this.devModeEnabled || !this.isSystemScheme
    },

    // 当前可用的字段配置（包含基础资料栏位的变更）
    currentFieldConfig() {
      return this.internalFieldConfig.length > 0 ? this.internalFieldConfig : this.fieldConfig
    }
  },
  watch: {
    currentScheme: {
      handler(val, oldVal) {
        if (val) {
          this.setSchemeData(val)
        }
      },
      immediate: false,
      deep: false
    },
  },
  methods: {
    // ==========  开发模式相关方法 ==========

    // 处理方案名称点击事件（开发后门触发）
    handleSchemeNameClick(row) {
      // 如果没有启用开发后门功能，直接返回
      if (!this.enableDevModel) {
        return
      }

      // 只有系统方案才能触发开发模式
      if (row.type != 0) {
        return
      }

      this.devModeClickCount++

      // 清除之前的计时器
      if (this.devModeTimer) {
        clearTimeout(this.devModeTimer)
      }

      // 设置新的计时器，3秒后重置点击计数
      this.devModeTimer = setTimeout(() => {
        this.devModeClickCount = 0
      }, 3000)

      // 连续点击5次系统方案名称开启开发模式
      if (this.devModeClickCount >= 5) {
        this.enableDevMode()
        this.devModeClickCount = 0
        if (this.devModeTimer) {
          clearTimeout(this.devModeTimer)
          this.devModeTimer = null
        }
      }
    },

    // 开启开发模式
    enableDevMode() {
      const processEnv = process.env.NODE_ENV === 'production'
      if(processEnv){
        this.$message.error('生产环境禁用开发模式！')
        return
      }
      this.devModeEnabled = true
      this.$message.success('开发模式已开启！')

      // 开发模式10分钟后自动关闭
      setTimeout(() => {
        this.disableDevMode()
      }, 10 * 60 * 1000) // 10分钟
    },

    // 关闭开发模式
    disableDevMode() {
      if (this.devModeEnabled) {
        this.devModeEnabled = false
        this.$message.warning('开发模式已自动关闭!')
      }
    },

    // 重置开发模式状态（组件关闭时调用）
    resetDevMode() {
      // 清理开发模式相关状态
      if (this.devModeEnabled) {
        this.devModeEnabled = false
        // 组件关闭时不显示消息，静默重置
      }

      // 重置点击计数
      this.devModeClickCount = 0

      // 清理计时器
      if (this.devModeTimer) {
        clearTimeout(this.devModeTimer)
        this.devModeTimer = null
      }
    },
    // 初始化内部字段配置
    initInternalFieldConfig() {
      // 创建fieldConfig的深拷贝作为内部配置
      this.internalFieldConfig = JSON.parse(JSON.stringify(this.fieldConfig))

      // 同步现有栏位设置中的别名到内部字段配置
      this.syncFieldConfigWithColumnAlias()
    },

    // 同步栏位设置中的别名到内部字段配置
    syncFieldConfigWithColumnAlias() {
      if (this.columnContent && this.columnContent.length > 0) {
        this.internalFieldConfig.forEach(fieldConfig => {
          const columnItem = this.columnContent.find(col => col.field === fieldConfig.field)
          if (columnItem && columnItem.rem_gb) {
            // 如果栏位设置中有别名，则使用别名作为显示标题
            fieldConfig.title = columnItem.rem_gb
            fieldConfig.label = columnItem.rem_gb
          }
        })
      }
    },

    // 同步内部字段配置（更新可用字段列表）
    syncInternalFieldConfig(enabledFields, disabledFields) {
      // 添加新启用的字段到内部字段配置
      if (enabledFields.length > 0) {
        enabledFields.forEach(field => {
          // 检查是否已存在该字段
          const existingField = this.internalFieldConfig.find(config => config.field === field.field)
          if (!existingField) {
            // 查找对应的栏位设置，获取别名信息
            const columnItem = this.columnContent.find(col => col.field === field.field)
            const displayTitle = (columnItem && columnItem.rem_gb) ? columnItem.rem_gb : field.title

            // 添加新字段到内部配置
            const newFieldConfig = {
              field: field.field,
              title: displayTitle,
              label: displayTitle,
              component: 'input', // 默认组件类型
              width: '120' // 默认宽度
            }
            this.internalFieldConfig.push(newFieldConfig)
          }
        })
      }

      // 移除被禁用的字段
      if (disabledFields.length > 0) {
        disabledFields.forEach(field => {
          const fieldIndex = this.internalFieldConfig.findIndex(config => config.field === field.field)
          if (fieldIndex !== -1) {
            this.internalFieldConfig.splice(fieldIndex, 1)
          }
        })
      }
    },

    // 保存成功后同步更新schemeData中的数据
    syncSchemeDataAfterSave(savedSchemeData) {
      if (!savedSchemeData || !savedSchemeData.id) return

      // 创建深拷贝的数据，避免引用问题
      const updatedSchemeData = JSON.parse(JSON.stringify(savedSchemeData))

      // 更新schemeData中对应的方案数据
      const schemeIndex = this.schemeData.findIndex(item => item.id == savedSchemeData.id)
      if (schemeIndex !== -1) {
        // 使用Vue.set确保响应式更新
        this.$set(this.schemeData, schemeIndex, updatedSchemeData)
      }

      // 同步更新originalSchemeData（用于搜索过滤的原始数据）
      const originalIndex = this.originalSchemeData.findIndex(item => item.id == savedSchemeData.id)
      if (originalIndex !== -1) {
        this.$set(this.originalSchemeData, originalIndex, JSON.parse(JSON.stringify(updatedSchemeData)))
      }

      // 同步更新listGridOptions.data（当前显示的数据）
      const listIndex = this.listGridOptions.data.findIndex(item => item.id == savedSchemeData.id)
      if (listIndex !== -1) {
        this.$set(this.listGridOptions.data, listIndex, JSON.parse(JSON.stringify(updatedSchemeData)))
      }

      // 更新当前方案引用，确保界面显示的是最新数据
      if (this.currentScheme && this.currentScheme.id == savedSchemeData.id) {
        this.currentScheme = updatedSchemeData
      }
    },

    setSchemeData(row){
      if (!row || !row.name || !this.schemeData) return

      const schemeItem = this.schemeData.find(item => item.name === row.name)
      if (!schemeItem) return

      const {sort_content,column_content,cond_content,search_content } = schemeItem

      // 处理快速过滤条件中的动态日期操作符
      if (cond_content && Array.isArray(cond_content)) {
        this.condContent = SchemeDataUtils.processLoadedConditions([...cond_content])
      } else {
        this.condContent = []
      }
      this.columnContent = column_content ? [...column_content] : []
      // 解析排序内容，确保返回数组格式
      let parsed = []
      if (sort_content && typeof sort_content === 'string' && sort_content.trim()) {
        parsed = sort_content
          .split(',')
          .map(item => item.trim())
          .filter(item => item)
          .map(item => {
            const parts = item.split(/\s+/)
            const field = parts[0]
            const sort = parts[1] || ''
            return { field, sort }
          })
          .filter(item => item.field) // 确保field存在
      }
      this.sortContent = parsed

      // 处理方案条件数据：如果是平铺格式（带groupid），则转换为分组格式
      if (search_content && Array.isArray(search_content)) {
        let processedSearchContent = search_content

        // 处理动态日期操作符，重新计算当前日期范围
        processedSearchContent = SchemeDataUtils.processLoadedConditions(processedSearchContent)

        if (processedSearchContent.length > 0 && processedSearchContent[0].hasOwnProperty('groupid')) {
          this.searchContent = SchemeDataUtils.convertFlatToGrouped(processedSearchContent)
        } else {
          this.searchContent = [...processedSearchContent]
        }
      } else {
        this.searchContent = []
      }

      // 设置栏位数据
      this.$nextTick(() => {
        this.initColumnData()
        this.initSortData()
        // 同步栏位别名到内部字段配置
        this.syncFieldConfigWithColumnAlias()
      })
    },

    // ==========  方案条件数据操作方法 ==========

    // 处理方案条件变更
    handleSchemeConditionChange(groupIndex, conditionIndex, newCondition) {
      if (this.searchContent[groupIndex] && this.searchContent[groupIndex].ITEMS) {
        this.$set(this.searchContent[groupIndex].ITEMS, conditionIndex, newCondition)
      }
    },

    // 删除方案条件组
    handleSchemeConditionGroupDelete(groupIndex) {
      this.searchContent.splice(groupIndex, 1)

    },

    // 添加方案条件组
    handleSchemeConditionGroupAdd() {
      // 计算新组的 groupId（取当前最大 groupId + 1）
      let maxGroupId = 0
      this.searchContent.forEach(group => {
        if (group.groupId && group.groupId > maxGroupId) {
          maxGroupId = group.groupId
        }
      })

      const newGroup = {
        groupId: maxGroupId + 1,
        ITEMS: []
      }
      this.searchContent.push(newGroup)

    },

    // ==========  快速过滤数据操作方法 ==========

    // 处理临时条件变更
    handleTempConditionChange(index, newCondition) {
      const {label, component, ...newCond} = newCondition
      this.$set(this.condContent, index, newCond)

    },

    // 删除临时条件
    handleTempConditionDelete(index) {
      // 直接从数组中移除条件，不再使用标识控制
      this.condContent.splice(index, 1)

    },

    // 删除全部临时条件
    handleTempDelAllCondition(){
      this.condContent = []

    },

    // 临时添加条件（在 popover 中使用）
    handleTempAddCondition(fieldValue) {
      console.log('临时添加条件:', fieldValue)
      if (!fieldValue) return

      // 检查是否已经存在该字段的条件
      const existingCondition = this.condContent.find(condition =>
        condition.field === fieldValue
      )
      if (existingCondition) {
        this.$message.error('该字段的查询条件已存在')
        return
      }

      // 检查条件数量限制
      const activeConditionsCount = this.condContent.length
      if (activeConditionsCount >= this.maxConditions) {
        this.$message.error(`最多只能添加${this.maxConditions}个查询条件`)
        return
      }

      // 获取字段配置
      const fieldConfig = this.currentFieldConfig.find(f => f.field === fieldValue)
      if (!fieldConfig) {
        this.$message.error('字段配置不存在')
        return
      }

      // 确定组件类型
      const componentType = fieldConfig.component || 'input'

      // 创建新条件
      const newCondition = {
        field: fieldValue,
        operator: getDefaultOperator(componentType),
        value: fieldConfig.value !== undefined ? fieldConfig.value : null,
        component: componentType,
        label: fieldConfig.title || fieldConfig.label,
        _INSERT: 1,
      }

      console.log(`临时添加条件 ${fieldValue}:`, {
        组件类型: componentType,
        默认操作符: newCondition.operator,
        初始值: newCondition.value,
        字段配置: fieldConfig
      })

      this.condContent.push(newCondition)

      // 清空选择框
      this.$nextTick(() => {
        if (this.$refs.addConditionSelect) {
          this.$refs.addConditionSelect.blur()
        }
      })
      console.log('临时条件列表更新:', this.condContent)
    },

    // ==========  栏位设置数据操作方法 ==========

    // 配置基础资料栏位
    handleConfigBaseFields() {
      if (this.$refs.baseFieldConfigDialog) {
        this.$refs.baseFieldConfigDialog.show()
      }
    },

    // 处理基础资料栏位配置确认
    handleBaseFieldConfigConfirm(changeResult) {
      console.log('字段变更结果:', changeResult)

      const { enabledFields, disabledFields } = changeResult
      let operationCount = 0
      let operationMessages = []

      // 处理添加的字段（添加到栏位设置）
      if (enabledFields.length > 0) {
        enabledFields.forEach(field => {
          // 检查是否已存在该字段（双重保险）
          const existingField = this.columnContent.find(col => col.field === field.field)
          if (!existingField) {
            const newColumn = {
              field: field.field,
              title: field.title,
              rem_gb: '', // 栏位别名
              size: '', // 默认宽度
              is_show: 'T', // 默认显示
              used: 'F', // 默认不禁用
            }
            this.columnContent.push(newColumn)
            operationCount++
          }
        })
        if (enabledFields.length > 0) {
          operationMessages.push(`添加 ${enabledFields.length} 个字段`)
        }
      }

      // 处理移除的字段（从栏位设置移除）
      if (disabledFields.length > 0) {
        disabledFields.forEach(field => {
          const fieldIndex = this.columnContent.findIndex(col => col.field === field.field)
          if (fieldIndex !== -1) {
            this.columnContent.splice(fieldIndex, 1)
            operationCount++
          }
        })
        if (disabledFields.length > 0) {
          operationMessages.push(`移除 ${disabledFields.length} 个字段`)
        }
      }

      // 更新原始数据和表格显示
      this.originalColumnContent = [...this.columnContent]
      this.handleColumnSearch() // 重新应用搜索过滤

      // 同步更新排序设定
      this.syncSortData()

      // 同步更新内部字段配置（可用字段列表）
      this.syncInternalFieldConfig(enabledFields, disabledFields)

      // 显示操作结果
      if (operationCount > 0) {
        this.$message.success(`栏位设置已更新：${operationMessages.join('，')}`)
      }
    },

    // 初始化栏位数据
    initColumnData() {
      // 如果没有栏位数据，创建默认栏位数据
      if (!this.columnContent || this.columnContent.length === 0) {
        this.columnContent = this.createDefaultColumnData()
      }

      // 保存原始数据用于搜索
      this.originalColumnContent = [...this.columnContent]
      this.columnGridOptions.data = [...this.columnContent]

      // 清空搜索关键词和重置选中行索引
      this.columnSearchKeyword = ''
      this.currentColumnRowIndex = null
    },

    // 创建默认栏位数据
    createDefaultColumnData() {
      // 基于currentFieldConfig创建默认栏位数据
      return this.currentFieldConfig.map((field, index) => ({
        field: field.field || `field_${index + 1}`,
        title: field.title || field.label || `栏位${index + 1}`,
        rem_gb: '', // 栏位别名
        size: field.width || '120', // 自定义宽度
        is_show: 'T', // 默认显示
        used: 'F', // 默认不禁用
        FLD_INDEX: index + 1
      }))
    },

    // 栏位搜索
    handleColumnSearch() {
      if (!this.columnSearchKeyword || this.columnSearchKeyword.trim() === '') {
        // 如果搜索关键词为空，显示所有数据
        this.columnGridOptions.data = [...this.originalColumnContent]
      } else {
        // 根据栏位代号或栏位名称进行模糊搜索
        const keyword = this.columnSearchKeyword.trim().toLowerCase()
        this.columnGridOptions.data = this.originalColumnContent.filter(column => {
          const fieldName = (column.field || '').toLowerCase()
          const fieldTitle = (column.title || '').toLowerCase()
          return fieldName.includes(keyword) || fieldTitle.includes(keyword)
        })
      }
      // 搜索后重置选中行索引
      this.currentColumnRowIndex = null
    },

    // 栏位表格行点击
    handleColumnCellClick({row, column}) {
      console.log('栏位行点击:', row, column)
      // 获取当前点击行在显示数据中的索引
      this.currentColumnRowIndex = this.columnGridOptions.data.findIndex(item =>
        item.field === row.field
      )
      console.log('当前选中栏位行索引:', this.currentColumnRowIndex)
    },

    // 栏位显示状态变更
    handleColumnShowChange(row) {
      console.log('栏位显示状态变更:', row.field, row.is_show)

      // 如果开启显示，则关闭禁用
      if (row.is_show === 'T') {
        row.used = 'F'
      }

      // 更新原始数据
      const originalRow = this.originalColumnContent.find(item => item.field === row.field)
      if (originalRow) {
        originalRow.is_show = row.is_show
        originalRow.used = row.used
      }
      // 更新columnContent
      const columnRow = this.columnContent.find(item => item.field === row.field)
      if (columnRow) {
        columnRow.is_show = row.is_show
        columnRow.used = row.used
      }

      // 同步更新排序设定
      this.syncSortData()



    },

    // 栏位禁用状态变更
    handleColumnDisableChange(row) {
      console.log('栏位禁用状态变更:', row.field, row.used)

      // 如果开启禁用，则关闭显示
      if (row.used === 'T') {
        row.is_show = 'F'
      }

      // 更新原始数据
      const originalRow = this.originalColumnContent.find(item => item.field === row.field)
      if (originalRow) {
        originalRow.is_show = row.is_show
        originalRow.used = row.used
      }
      // 更新columnContent
      const columnRow = this.columnContent.find(item => item.field === row.field)
      if (columnRow) {
        columnRow.is_show = row.is_show
        columnRow.used = row.used
      }

      // 同步更新排序设定
      this.syncSortData()



    },

    // 栏位别名变更
    handleColumnAliasChange(row) {
      console.log('栏位别名变更:', row.field, row.rem_gb)
      // 更新原始数据
      const originalRow = this.originalColumnContent.find(item => item.field === row.field)
      if (originalRow) {
        originalRow.rem_gb = row.rem_gb
      }
      // 更新columnContent
      const columnRow = this.columnContent.find(item => item.field === row.field)
      if (columnRow) {
        columnRow.rem_gb = row.rem_gb
      }

      // 同步更新排序设定中的标题（如果该字段在排序设定中存在）
      const sortItem = this.sortContent.find(item => item.field === row.field)
      if (sortItem) {
        // 使用栏位别名作为显示标题，如果别名为空则使用原标题
        sortItem.title = row.rem_gb || row.title
        // 同步更新原始排序数据
        const originalSortItem = this.originalSortContent.find(item => item.field === row.field)
        if (originalSortItem) {
          originalSortItem.title = row.rem_gb || row.title
        }
        // 重新应用搜索过滤
        this.handleSortSearch()
      }

      // 同步更新内部字段配置中的标题（影响可用字段列表显示）
      const fieldConfigItem = this.internalFieldConfig.find(item => item.field === row.field)
      if (fieldConfigItem) {
        // 使用栏位别名作为显示标题，如果别名为空则使用原标题
        const displayTitle = row.rem_gb || row.title
        fieldConfigItem.title = displayTitle
        fieldConfigItem.label = displayTitle

        console.log('内部字段配置标题已更新:', {
          字段: row.field,
          新标题: displayTitle,
          原标题: row.title,
          别名: row.rem_gb
        })
      }

    },

    // 栏位宽度变更
    handleColumnWidthChange(row) {
      console.log('栏位宽度变更:', row.field, row.size)
      // 更新原始数据
      const originalRow = this.originalColumnContent.find(item => item.field === row.field)
      if (originalRow) {
        originalRow.size = row.size
      }
      // 更新columnContent
      const columnRow = this.columnContent.find(item => item.field === row.field)
      if (columnRow) {
        columnRow.size = row.size
      }



    },

    // ==========  栏位排序操作方法 ==========

    // 刷新栏位表格渲染
    refreshColumnGrid() {
      this.$nextTick(() => {
        if (this.$refs.columnGridRef && typeof this.$refs.columnGridRef.loadData === 'function') {
          try {
            this.$refs.columnGridRef.loadData(this.columnGridOptions.data)
          } catch (error) {
            console.warn('VXE Grid loadData error:', error)
          }
        }
      })
    },

    // 栏位移动到顶部
    moveColumnTop() {
      if (this.currentColumnRowIndex === null || this.currentColumnRowIndex === undefined) {
        return
      }

      const displayData = this.columnGridOptions.data
      const originalData = this.originalColumnContent
      const columnData = this.columnContent

      if (this.currentColumnRowIndex === 0) {
        return
      }

      // 移动显示数据
      const item = displayData.splice(this.currentColumnRowIndex, 1)[0]
      displayData.unshift(item)

      // 移动原始数据
      const originalIndex = originalData.findIndex(row => row.field === item.field)
      if (originalIndex !== -1) {
        const originalItem = originalData.splice(originalIndex, 1)[0]
        originalData.unshift(originalItem)
      }

      // 移动栏位数据
      const columnIndex = columnData.findIndex(row => row.field === item.field)
      if (columnIndex !== -1) {
        const columnItem = columnData.splice(columnIndex, 1)[0]
        columnData.unshift(columnItem)
      }

      // 更新当前选中行索引
      this.currentColumnRowIndex = 0

      // 立即更新表格数据以触发重新渲染
      this.$set(this.columnGridOptions, 'data', [...displayData])

      // 刷新表格渲染
      this.refreshColumnGrid()

      // 设置当前行
      this.$nextTick(() => {
        if (this.$refs.columnGridRef) {
          this.$refs.columnGridRef.setCurrentRow(item)
        }
      })

      // 同步更新排序设定
      this.syncSortData()

      console.log('栏位移动到顶部完成')
    },

    // 栏位向上移动
    moveColumnUp() {
      if (this.currentColumnRowIndex === null || this.currentColumnRowIndex === undefined) {
        return
      }

      const displayData = this.columnGridOptions.data
      const originalData = this.originalColumnContent
      const columnData = this.columnContent

      if (this.currentColumnRowIndex === 0) {
        return
      }

      const currentIndex = this.currentColumnRowIndex
      const targetIndex = currentIndex - 1

      // 交换显示数据
      const temp = displayData[currentIndex]
      displayData[currentIndex] = displayData[targetIndex]
      displayData[targetIndex] = temp

      // 交换原始数据
      const currentOriginalIndex = originalData.findIndex(row => row.field === temp.field)
      const targetOriginalIndex = originalData.findIndex(row => row.field === displayData[currentIndex].field)
      if (currentOriginalIndex !== -1 && targetOriginalIndex !== -1) {
        const tempOriginal = originalData[currentOriginalIndex]
        originalData[currentOriginalIndex] = originalData[targetOriginalIndex]
        originalData[targetOriginalIndex] = tempOriginal
      }

      // 交换栏位数据
      const currentColumnIndex = columnData.findIndex(row => row.field === temp.field)
      const targetColumnIndex = columnData.findIndex(row => row.field === displayData[currentIndex].field)
      if (currentColumnIndex !== -1 && targetColumnIndex !== -1) {
        const tempColumn = columnData[currentColumnIndex]
        columnData[currentColumnIndex] = columnData[targetColumnIndex]
        columnData[targetColumnIndex] = tempColumn
      }

      // 更新当前选中行索引
      this.currentColumnRowIndex = targetIndex

      // 立即更新表格数据以触发重新渲染
      this.$set(this.columnGridOptions, 'data', [...displayData])

      // 刷新表格渲染
      this.refreshColumnGrid()

      // 设置当前行
      this.$nextTick(() => {
        if (this.$refs.columnGridRef) {
          this.$refs.columnGridRef.setCurrentRow(temp)
        }
      })

      // 同步更新排序设定
      this.syncSortData()

      console.log('栏位向上移动完成')
    },

    // 栏位向下移动
    moveColumnDown() {
      if (this.currentColumnRowIndex === null || this.currentColumnRowIndex === undefined) {
        return
      }

      const displayData = this.columnGridOptions.data
      const originalData = this.originalColumnContent
      const columnData = this.columnContent

      if (this.currentColumnRowIndex === displayData.length - 1) {
        return
      }

      const currentIndex = this.currentColumnRowIndex
      const targetIndex = currentIndex + 1

      // 交换显示数据
      const temp = displayData[currentIndex]
      displayData[currentIndex] = displayData[targetIndex]
      displayData[targetIndex] = temp

      // 交换原始数据
      const currentOriginalIndex = originalData.findIndex(row => row.field === temp.field)
      const targetOriginalIndex = originalData.findIndex(row => row.field === displayData[currentIndex].field)
      if (currentOriginalIndex !== -1 && targetOriginalIndex !== -1) {
        const tempOriginal = originalData[currentOriginalIndex]
        originalData[currentOriginalIndex] = originalData[targetOriginalIndex]
        originalData[targetOriginalIndex] = tempOriginal
      }

      // 交换栏位数据
      const currentColumnIndex = columnData.findIndex(row => row.field === temp.field)
      const targetColumnIndex = columnData.findIndex(row => row.field === displayData[currentIndex].field)
      if (currentColumnIndex !== -1 && targetColumnIndex !== -1) {
        const tempColumn = columnData[currentColumnIndex]
        columnData[currentColumnIndex] = columnData[targetColumnIndex]
        columnData[targetColumnIndex] = tempColumn
      }

      // 更新当前选中行索引
      this.currentColumnRowIndex = targetIndex

      // 立即更新表格数据以触发重新渲染
      this.$set(this.columnGridOptions, 'data', [...displayData])

      // 刷新表格渲染
      this.refreshColumnGrid()

      // 设置当前行
      this.$nextTick(() => {
        if (this.$refs.columnGridRef) {
          this.$refs.columnGridRef.setCurrentRow(temp)
        }
      })

      // 同步更新排序设定
      this.syncSortData()

      console.log('栏位向下移动完成')
    },

    // 栏位移动到底部
    moveColumnBottom() {
      if (this.currentColumnRowIndex === null || this.currentColumnRowIndex === undefined) {
        return
      }

      const displayData = this.columnGridOptions.data
      const originalData = this.originalColumnContent
      const columnData = this.columnContent

      if (this.currentColumnRowIndex === displayData.length - 1) {
        return
      }

      // 移动显示数据
      const item = displayData.splice(this.currentColumnRowIndex, 1)[0]
      displayData.push(item)

      // 移动原始数据
      const originalIndex = originalData.findIndex(row => row.field === item.field)
      if (originalIndex !== -1) {
        const originalItem = originalData.splice(originalIndex, 1)[0]
        originalData.push(originalItem)
      }

      // 移动栏位数据
      const columnIndex = columnData.findIndex(row => row.field === item.field)
      if (columnIndex !== -1) {
        const columnItem = columnData.splice(columnIndex, 1)[0]
        columnData.push(columnItem)
      }

      // 更新当前选中行索引
      this.currentColumnRowIndex = displayData.length - 1

      // 立即更新表格数据以触发重新渲染
      this.$set(this.columnGridOptions, 'data', [...displayData])

      // 刷新表格渲染
      this.refreshColumnGrid()

      // 设置当前行
      this.$nextTick(() => {
        if (this.$refs.columnGridRef) {
          this.$refs.columnGridRef.setCurrentRow(item)
        }
      })

      // 同步更新排序设定
      this.syncSortData()

      console.log('栏位移动到底部完成')
    },

    // ==========  排序设定数据操作方法 ==========

    // 初始化排序数据
    initSortData() {
      // 基于显示的栏位创建排序数据
      const displayedColumns = this.columnContent.filter(column => column.is_show === 'T')

      // 确保sortContent是数组格式
      if (!Array.isArray(this.sortContent)) {
        this.sortContent = []
      }

      // 如果没有排序数据，创建默认排序数据
      if (this.sortContent.length === 0) {
        this.sortContent = displayedColumns.map((column, index) => ({
          field: column.field,
          title: column.rem_gb || column.title, // 优先使用栏位别名
          sort: '', // 默认不排序
        }))
      } else {
        // 如果有排序数据，需要补充缺失的字段并同步显示的栏位

        // 首先补充现有排序数据的title字段（如果缺失）
        this.sortContent.forEach(sortItem => {
          if (!sortItem.title) {
            const column = displayedColumns.find(col => col.field === sortItem.field)
            if (column) {
              sortItem.title = column.rem_gb || column.title // 优先使用栏位别名
            }
          }
        })

        const existingSortFields = this.sortContent.map(item => item.field)

        // 添加新显示的栏位到排序设定中
        displayedColumns.forEach(column => {
          if (!existingSortFields.includes(column.field)) {
            this.sortContent.push({
              field: column.field,
              title: column.rem_gb || column.title, // 优先使用栏位别名
              sort: '',
            })
          }
        })

        // 移除不再显示的栏位
        this.sortContent = this.sortContent.filter(sortItem =>
          displayedColumns.some(column => column.field === sortItem.field)
        )

        // 更新栏位标题（可能在栏位设置中被修改）
        this.sortContent.forEach(sortItem => {
          const column = displayedColumns.find(col => col.field === sortItem.field)
          if (column) {
            sortItem.title = column.rem_gb || column.title // 优先使用栏位别名
          }
        })
      }

      // 保存原始数据用于搜索
      this.originalSortContent = [...this.sortContent]
      this.sortGridOptions.data = [...this.sortContent]

      // 清空搜索关键词
      this.sortSearchKeyword = ''
    },

    // 同步排序数据（当栏位显示状态变更时调用）
    syncSortData() {
      // 确保sortContent是数组格式
      if (!Array.isArray(this.sortContent)) {
        this.sortContent = []
      }

      // 获取当前显示的栏位
      const displayedColumns = this.columnContent.filter(column => column.is_show === 'T')
      const displayedFieldNames = displayedColumns.map(col => col.field)

      // 移除不再显示的栏位的排序设定
      this.sortContent = this.sortContent.filter(sortItem =>
        displayedFieldNames.includes(sortItem.field)
      )

      // 添加新显示的栏位到排序设定
      const existingSortFields = this.sortContent.map(item => item.field)
      displayedColumns.forEach(column => {
        if (!existingSortFields.includes(column.field)) {
          this.sortContent.push({
            field: column.field,
            title: column.rem_gb || column.title, // 优先使用栏位别名
            sort: '',
          })
        }
      })

      // 更新栏位标题
      this.sortContent.forEach(sortItem => {
        const column = displayedColumns.find(col => col.field === sortItem.field)
        if (column) {
          sortItem.title = column.rem_gb || column.title // 优先使用栏位别名
        }
      })

      // 更新表格数据
      this.originalSortContent = [...this.sortContent]
      this.handleSortSearch() // 重新应用搜索过滤
    },

    // 排序搜索
    handleSortSearch() {
      if (!this.sortSearchKeyword || this.sortSearchKeyword.trim() === '') {
        // 如果搜索关键词为空，显示所有数据
        this.sortGridOptions.data = [...this.originalSortContent]
      } else {
        // 根据栏位代号或栏位名称进行模糊搜索
        const keyword = this.sortSearchKeyword.trim().toLowerCase()
        this.sortGridOptions.data = this.originalSortContent.filter(sortItem => {
          const fieldName = (sortItem.field || '').toLowerCase()
          const fieldTitle = (sortItem.title || '').toLowerCase()
          return fieldName.includes(keyword) || fieldTitle.includes(keyword)
        })
      }
    },

    // 排序表格行点击
    handleSortCellClick({row, column}) {
      console.log('排序行点击:', row, column)
    },

    // 排序方式变更
    handleSortTypeChange(row) {
      console.log('排序方式变更:', row.field, row.sort)

      // 更新原始数据
      const originalRow = this.originalSortContent.find(item => item.field === row.field)
      if (originalRow) {
        originalRow.sort = row.sort
      }

      // 更新sortContent
      const sortRow = this.sortContent.find(item => item.field === row.field)
      if (sortRow) {
        sortRow.sort = row.sort
      }



    },

    // ===========================================
    // 搜索过滤方案数据
    handleSearch() {
      if (!this.searchKeyword || this.searchKeyword.trim() === '') {
        // 如果搜索关键词为空，显示所有数据
        this.listGridOptions.data = [...this.originalSchemeData]
      } else {
        // 根据方案名称进行模糊搜索
        const keyword = this.searchKeyword.trim().toLowerCase()
        this.listGridOptions.data = this.originalSchemeData.filter(scheme => {
          return scheme.name && scheme.name.toLowerCase().includes(keyword)
        })
      }

      // 搜索后重新设置当前选中项
      this.$nextTick(() => {
        if (this.listGridOptions.data.length > 0) {
          // 如果当前选中的方案还在过滤结果中，保持选中
          const currentInFiltered = this.listGridOptions.data.find(item =>
            this.currentScheme && item.id === this.currentScheme.id
          )

          if (currentInFiltered) {
            this.setCurrentRow(currentInFiltered)
          } else {
            // 否则选中第一个
            this.currentScheme = this.listGridOptions.data[0]
            this.setCurrentRow(this.listGridOptions.data[0])
          }
        } else {
          // 没有搜索结果时清空当前选中
          this.currentScheme = null
        }
      })
    },
    // 获取方案类型名称
    getSchemeTypeName(type) {
      return this.schemeTypeMap[Number(type)] || '未知类型'
    },

    // 加载方案数据
    async loadSchemeData() {
      try {
        this.listGridOptions.loading = true
        const response = await getSchemeSet({FUNID:this.FUNID}, this.MODULEValue)
        if (response && response.data) {
          const schemeData = response.data
          this.schemeData = JSON.parse(JSON.stringify(schemeData)) // 数据集合
          this.originalSchemeData = JSON.parse(JSON.stringify(schemeData)) // 数据集合拷贝，用于查询数据
          this.listGridOptions.data = JSON.parse(JSON.stringify(schemeData)) // 当前数据
        }
        // 清空搜索关键词
        this.searchKeyword = ''

        // 由外部传入当前激活的方案
        this.$nextTick(() => {
          if (this.listGridOptions.data.length > 0) {
            const row = this.listGridOptions.data.find(item => item.id == this.currentView.id)
            if (row) {
              this.currentScheme = row  // 设置当前方案
              this.setCurrentRow(row)   // 设置表格当前行
            }
          }
        })

      } catch (error) {
        this.requestFailed(error)
      } finally {
        this.listGridOptions.loading = false
      }
    },
    // 设置当前行
    setCurrentRow(row) {
      if (this.$refs.listGridRef) {
        this.$refs.listGridRef.setCurrentRow(row)
      }
    },
    getMonthRange() {
      const now = new Date()
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

      return [
        startOfMonth.toISOString().split('T')[0],
        endOfMonth.toISOString().split('T')[0]
      ]
    },
    // 行选中判断方法
    rowCurrentMethod({ row }) {
      return this.currentScheme && row.id === this.currentScheme.id
    },

    handleCellClick({row, column}){
      // 如果点击的是当前方案，不需要处理
      if (this.currentScheme && row.id === this.currentScheme.id) {
        return
      }
      this.currentScheme = row
      this.setCurrentRow(row)
    },
    handleSaveAs(){
      if (!this.currentScheme) {
        this.$message.error('请先选择一个方案')
        return
      }

      // 重置表单数据
      this.saveAsForm = {
        name: '',
        type: 2 // 默认为我的方案
      }

      // 显示另存为对话框
      this.saveAsDialogVisible = true
    },

    // 另存为对话框确认
    async handleSaveAsConfirm() {
      // 验证表单
      this.$refs.saveAsForm.validate(async (valid) => {
        if (valid) {
          try {
            // 构建另存为的数据，包含当前的修改内容
            const data = {
              id: this.currentScheme.id,
              name: this.saveAsForm.name,
              type: this.saveAsForm.type,
            }

            const response = await saveAsScheme(data, this.MODULEValue)
            this.$message.success(this.$t("public.success"))
            this.saveAsDialogVisible = false
            await this.loadSchemeData()
            this.refresh = true // 标记刷新
          } catch (error) {
            this.requestFailed(error)
          }
        }
      })
    },

    // 另存为对话框取消
    handleSaveAsCancel() {
      this.saveAsDialogVisible = false
      // 重置表单
      this.$refs.saveAsForm.resetFields()
    },
    async handleShow(){
      this.dialogVisible = true
      // 初始化内部字段配置
      this.initInternalFieldConfig()
      await this.loadSchemeData()
    },
    handleAdd() {
      // 创建新方案
      const newId = String(this.originalSchemeData.length + 1)
      const newScheme = {
        id: newId,
        name: `新方案 ${newId}`,
        is_show: 'T',
        type: 3 // 新建方案默认为我的方案
      }

      // 同时添加到原始数据和显示数据
      this.originalSchemeData.push(newScheme)
      this.listGridOptions.data.push(newScheme)

      this.currentScheme = newScheme
      this.setCurrentRow(newScheme)
    },
    handleDelete() {
      // 系统方案不允许删除
      if (this.isSystemScheme) {
        this.$message.error('系统方案不允许删除')
        return Promise.reject('系统方案不允许删除')
      }

      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteScheme({id:this.currentScheme.id}, this.MODULEValue)

          // 从原始数据和显示数据中移除
          const originalIndex = this.originalSchemeData.findIndex(scheme => scheme.id === this.currentScheme.id)
          const displayIndex = this.listGridOptions.data.findIndex(scheme => scheme.id === this.currentScheme.id)

          if (originalIndex !== -1) {
            this.originalSchemeData.splice(originalIndex, 1)
          }
          if (displayIndex !== -1) {
            this.listGridOptions.data.splice(displayIndex, 1)
          }

          // 重新设置选中的方案
          if (this.listGridOptions.data.length > 0 && response.data) {
            this.currentScheme = this.listGridOptions.data.find(item => item.id === response.data)
            this.setCurrentRow(this.currentScheme)
          } else {
            this.currentScheme = null
          }

          this.$message.success(this.$t('public.success'))
          this.refresh = true // 标记刷新
        } catch (error) {
          this.requestFailed(error)
        }
      }).catch(() => {
        // 取消删除
      })
    },
    async handleApply(){
      try{
        await setCurScheme({id: this.currentScheme.id}, this.MODULEValue)
        this.$message.success(this.$t("public.success"))
        this.refresh = true // 标记刷新
        this.handleClose()
      }catch (err){
        this.requestFailed(err)
      }
    },
    handleClose() {
      // 重置数据状态
      this.resetDialogData()
      this.dialogVisible = false
      if(this.refresh){
        this.$emit('refresh')
      }
    },

    // 重置弹窗数据
    resetDialogData() {
      this.currentScheme = null
      this.activeTab = 'conditions'
      this.searchKeyword = ''
      this.columnSearchKeyword = ''
      this.sortSearchKeyword = ''
      this.condContent = []
      this.columnContent = []
      this.sortContent = []
      this.searchContent = []
      this.originalColumnContent = []
      this.originalSchemeData = []
      this.originalSortContent = []
      this.currentColumnRowIndex = null // 重置当前选中的栏位行索引
      this.internalFieldConfig = [] // 重置内部字段配置

      // 重置另存为对话框数据
      this.saveAsDialogVisible = false
      this.saveAsForm = {
        name: '',
        type: 2
      }

      // 清空表格数据
      this.listGridOptions.data = []
      this.columnGridOptions.data = []
      this.sortGridOptions.data = []

      // 重置开发模式状态（组件关闭时退出后门）
      this.resetDevMode()
    },
    async handleSave() {
      try {
        // 系统方案不允许保存（除非开启开发模式）
        if (this.isSystemScheme && !this.devModeEnabled) {
          this.$message.error('系统方案不允许修改')
          return Promise.reject('系统方案不允许修改')
        }

        // 如果是开发模式下保存系统方案，给出警告提示
        if (this.isSystemScheme && this.devModeEnabled) {
          try {
            await this.$confirm('您正在开发模式下修改系统方案，这可能影响所有用户。确定要继续吗？', '开发模式警告', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
          } catch {
            return Promise.reject('用户取消操作')
          }
        }

        // 校验方案条件
        const validationResult = this.validateSchemeConditions()
        if (!validationResult.valid) {
          this.$message.error(validationResult.message)
          return Promise.reject(validationResult.message)
        }

        // 更新当前方案的栏位设置数据
        if (this.currentScheme) {
          const currentSchemeData = this.currentScheme
          if (currentSchemeData) {
            currentSchemeData.search_content = SchemeDataUtils.convertGroupedToFlat(this.searchContent)
            currentSchemeData.column_content = this.columnContent
            // 处理快速过滤条件中的动态日期操作符
            currentSchemeData.cond_content = this.processCondContentForSave(this.condContent)
            const sortStr = this.sortContent
              .filter(item => item.sort) // 过滤出 sort 有值的项
              .map(item => `${item.field} ${item.sort}`) // 拼接为 "field sort"
              .join(', ') // 用逗号加空格连接
            currentSchemeData.sort_content = sortStr
            currentSchemeData.FUNID = this.FUNID
            const response = await saveScheme(currentSchemeData,this.MODULEValue)
            this.$message.success(this.$t("public.success"))

            // 保存成功后同步更新schemeData中的数据
            this.syncSchemeDataAfterSave(currentSchemeData)
            this.refresh = true // 标记刷新
          }
        }
        return Promise.resolve()
      } catch (error) {
        this.requestFailed(error)
      }
    },

    // 处理快速过滤条件保存时的动态日期操作符
    processCondContentForSave(condContent) {
      if (!Array.isArray(condContent) || condContent.length === 0) {
        return condContent
      }

      return condContent.map(condition => {
        // 对动态日期操作符，将 value 设为 null
        if (condition.operator && isDynamicDateOperator(condition.operator)) {
          console.log(`保存快速过滤时处理动态日期操作符 ${condition.operator}，将 value 设为 null`)
          return {
            ...condition,
            value: null
          }
        }

        // 其他操作符保持原值
        return condition
      })
    },

    // 校验方案条件
    validateSchemeConditions() {
      // 如果没有方案条件，则校验通过
      if (!this.searchContent || this.searchContent.length === 0) {
        return { valid: true, message: '' }
      }

      // 遍历所有条件组
      for (let groupIndex = 0; groupIndex < this.searchContent.length; groupIndex++) {
        const group = this.searchContent[groupIndex]

        // 如果条件组没有条件项，跳过
        if (!group.ITEMS || group.ITEMS.length === 0) {
          continue
        }

        // 遍历条件组中的所有条件
        for (let conditionIndex = 0; conditionIndex < group.ITEMS.length; conditionIndex++) {
          const condition = group.ITEMS[conditionIndex]

          // 校验单个条件
          const validationResult = validateCondition(condition)
          if (!validationResult.valid) {
            return {
              valid: false,
              message: `方案条件:${validationResult.message}`
            }
          }
        }
      }

      return { valid: true, message: '' }
    },
  },
}
</script>

<style lang="less" scoped>
// 输入框高度调整
::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}
::v-deep  .el-input__icon {
  line-height: 32px;
}


.scheme-dialog-container {
  .scheme-dialog-content {
    display: flex;
    flex-direction: column;
    height: 600px;
  }
  
  // 工具栏样式
  .toolbar {
    display: flex;
    height: 45px;
    background: #FFFFFF;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    z-index: 1;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);

    .toolbar-title {
      display: flex;
      justify-content: center;
      align-items: center;

      &::after {
        content: '';
        display: block;
        width: 1px;
        height: 22px;
        background: rgba(0, 0, 0, 0.2);
        margin: 0 16px;
      }

      span {
        height: 20px;
        line-height: 20px;
        font-weight: 600;
        margin-left: 16px;
      }
    }

    .toolbar-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      ::v-deep .el-button {
        border: none;
        box-shadow: none;
        border-radius: 0;
        height: 45px;
        margin-left: 0;
        padding: 8px 15px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;

        span {
          display: flex;
          align-items: center;

          svg {
            color: var(--el-color-primary);
            margin-right: 3px;
          }
        }

        &:hover {
          color: white;
          background-color: var(--el-color-primary);

          svg {
            color: white;
          }
        }

        &.is-disabled {
          color: #747474;
          pointer-events: none;
          cursor: default;

          svg {
            color: #747474;
          }
        }
      }
    }
    .skip-btn {
      margin-left: auto;
      display: flex;
      align-items: center;
      padding: 0 16px;
      margin-right: 10px;
      flex-shrink: 0;
      .el-button+.el-button {
        margin-left: 1px;
      }

    }
    .drop-btn {
      margin-left: 10px;
    }

    .dropSet {
      padding: 6px 12px;
      font-size: 18px;
      border: var(--el-color-primary) 1px solid;
      color: var(--el-color-primary);
      background-color: #FFFFFF;
    }

    .dropSet:hover {
      background-color: #E6F8F3;
      border: var(--el-color-primary) 1px solid;
      color: var(--el-color-primary);
    }

    .design-btn {
      margin-left: auto;
      display: flex;
      align-items: center;
      padding: 0 16px;
      margin-right: 10px;
      flex-shrink: 0;

      .design-tips {
        margin-right: 15px;

        .tips-wrapper {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          background-color: #f0f9ff;
          border: 1px solid #409eff;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: #e6f7ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }

          i {
            color: #409eff;
            font-size: 16px;
            margin-right: 6px;
          }

          .tips-text {
            color: #409eff;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }

      .el-button {
        margin-left: 10px;
      }

      .el-button:first-child {
        margin-left: 0;
      }
    }
  }

  // 操作区域样式
  .operation-section {
    display: flex;
    flex: 1;
    overflow: hidden;
    margin-top: 10px;
  }
  
  // 左侧方案列表
  .scheme-list-section {
    width: 300px;
    display: flex;
    flex-direction: column;
  }
  
  .scheme-search {
    margin-bottom: 5px;
  }
  
  .scheme-list {
    flex: 1;
    overflow-y: auto;
  }

  // 右侧方案详情
  .scheme-detail-section {
    flex: 1;
    padding-left: 15px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .scheme-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .scheme-title {
    flex: 1;
  }
  
  .scheme-tabs {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    ::v-deep .el-tabs__content {
      flex: 1;
      overflow: hidden;
    }

    ::v-deep .el-tab-pane {
      height: 100%;
      overflow: hidden;
    }
  }
  
  .tab-content {
    height: 500px;
    overflow-y: auto;
    border-radius: 4px;
  }
  
  .placeholder-content {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    color: #909399;
    font-size: 14px;

    &:hover {
      background-color: #eef1f6;
    }
  }
  
  .no-scheme-selected {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .empty-state {
    text-align: center;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 10px;
    }
  }

  // switch-cell样式
  .switch-cell {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 栏位设置样式
  .column-settings {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .column-search-section {
    margin-bottom: 5px;
  }

  .column-search-row {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .column-search-input {
    flex: 1;
  }

  .config-base-fields-btn {
    flex-shrink: 0;
    white-space: nowrap;
  }

  .column-table-section {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .column-table-container {
    display: flex;
    height: 100%;
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .column-table-container .vxe-grid-common {
    flex: 1;
    min-width: 0; /* 防止flex子项溢出 */
    transition: width 0.2s ease; /* 添加平滑过渡 */
  }

  .column-sort-buttons {
    width: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;

    .sort-button {
      width: 32px;
      height: 32px;
      padding: 0;
      margin-left: 0;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        font-size: 14px;
      }
    }
  }

  // 排序设定样式
  .sort-settings {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .sort-search-section {
    margin-bottom: 5px;
  }

  .sort-search-input {
    width: 100%;
  }

  .sort-table-section {
    flex: 1;
    overflow: hidden;
  }

  // 开发模式相关样式
  .scheme-name {
    display: inline-flex;
    align-items: center;
    user-select: none;

    .dev-mode-indicator {
      margin-left: 5px;
      font-size: 12px;
      animation: pulse 1.5s infinite;
    }
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
}
</style>
