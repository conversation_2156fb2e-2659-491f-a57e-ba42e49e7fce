<template>
    <div class="search-form" v-loading="loading">
      <!-- 顶部查询方案区域 -->
      <div class="search-form-top">
        <div class="query-scheme-title">
          <span>查询方案</span>
        </div>
        <div class="button-container">
          <!-- 查询方案按钮 -->
          <el-button
            v-for="view in visibleViewList"
            :key="view.id"
            size="small"
            :type="currentView.id === view.id ? 'primary' : 'default'"
            @click="selectScheme(view.id)"
          >
            {{ view.name }}
          </el-button>
        </div>
        <div class="expand-box">
          <el-divider direction="vertical"></el-divider>
          <el-button type="text" @click="toggleFilter" class="filter-toggle">
            <span>{{ showFilter ? '隐藏过滤' : '显示过滤' }}</span>
            <i :class="showFilter ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
        </div>
      </div>
  
      <!-- 搜索条件区域 -->
      <div v-show="showFilter" class="search-form-content">
        <div class="search-form-field-item">
          <div class="quick-title">
            <span>快速过滤</span>
          </div>
          <div class="search-items">
            <el-row>
              <el-col :span="20">
                <el-row :gutter="8">
                  <!-- 搜索条件行 -->
                  <SearchFormItem
                    v-for="(condition, index) in searchConditions"
                    :key="condition.id"
                    :condition="condition"
                    :field-config="fieldConfigs"
                    :index="index"
                    :show-delete-button="false"
                    @condition-change="handleConditionChange"
                    @delete="handleConditionDelete"
                    @selectListEvent="handleSelectListEvent"
                    @iconClick="handleIconClick"
                  />
                </el-row>
              </el-col>
              <!-- 操作按钮区域 -->
              <el-col :span="4">
                <div class="action-buttons">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="handleSearch"
                    title="查询"
                  >
                    <i class="el-icon-search"></i>
                    查询
                  </el-button>
                  <el-popover
                    placement="bottom-end"
                    width="520"
                    trigger="click"
                    v-model="popoverVisible"
                    @show="handlePopoverShow">
                    <div class="condition-popover">
                      <scheme-filters
                        :conditions="tempSearchConditions"
                        :field-config="fieldConfigs"
                        @change="handleTempConditionChange"
                        @delete="handleTempConditionDelete"
                        @add="handleTempAddCondition"
                        @del-all="handleTempDelAllCondition"
                        @selectListEvent="handleSelectListEvent"
                        @iconClick="handleIconClick"
                      ></scheme-filters>
                      <div class="footer">
                        <div>
                          <el-button size="small" plain @click="handleRestore">恢复默认</el-button>
                        </div>
                        <div>
                          <el-button size="small" plain @click="handleCancel">取消</el-button>
                          <el-button size="small" type="primary" @click="handleSave" :disabled="isSystemScheme">保存</el-button>
                        </div>
                      </div>
                    </div>
                    <el-button slot="reference" class="setting-button" plain icon="el-icon-setting" size="small"></el-button>
                  </el-popover>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>

      <!-- 方案管理弹窗 -->
      <SchemeDialog ref="schemeDialogRef" :field-config="fieldConfigs" :currentView="currentView"
                    :FUNID="FUNID" :MODULEValue="MODULEValue"  :enableDevModel="enableDevModel"
                    @refresh="handleRefresh"
      />
    </div>
  </template>
<script>
import SearchFormItem from './src/SearchFormItem.vue'
import { formatFieldValue } from './mixins/fieldTypes'
import { isValidConditionValue } from './utils/valueValidator'
import { getOperatorsByType, getDefaultOperator } from './mixins/operators'
import FieldSelector from '@/components/amtxts/common/SearchForm/src/FieldSelector.vue'
import SchemeDialog from '@/components/amtxts/common/SchemeManager/SchemeDialog.vue'
import SchemeFilters from '@/components/amtxts/common/SchemeManager/SchemeFilters.vue'
import { getScheme, saveScheme, initScheme } from '@/api/admin/base'
import { SchemeDataUtils } from './utils/DataUtils'

export default {
name: 'SearchForm',
components: {
  SchemeFilters,
  FieldSelector,
  SearchFormItem,
  SchemeDialog
},
props: {
  // 业务扩展字段配置（用于补充接口数据）
  fieldConfigExtensions: {
    type: Array,
    default: () => []
  },
  // 最大条件数
  maxConditions: {
    type: Number,
    default: 12
  },
  // 功能ID
  FUNID: {
    type: String,
    required: true
  },
  // 模块名
  MODULE: {
    type: String,
    default: null
  },
  // 开发后门控制参数
  enableDevModel: {
    type: Boolean,
    default: false // 默认关闭后门功能
  }
},
data() {
  return {
    loading: false,
    showFilter: true,
    searchConditions: [], // 实际的查询条件数据
    tempSearchConditions: [], // 临时的查询条件数据（用于 popover 编辑）
    popoverVisible: false, // 控制 popover 显示状态
    // 字段配置数据（从接口获取并与扩展配置合并）
    fieldConfigs: [],

    // 视图相关数据
    currentView: null, // 当前激活的视图
    viewList: [], // 视图列表
    viewDataMap: {}, // 视图数据
  }
},
computed: {
  // 判断当前方案是否为系统方案
  isSystemScheme() {
    return this.currentView && this.currentView.type == 0
  },

  // 可见的视图列表
  visibleViewList() {
    return this.viewList?.filter(view => view.isShow === "T")
  },

  // 是否有有效条件
  hasValidConditions() {
    return this.searchConditions.some(condition =>
      condition.field && condition.operator && isValidConditionValue(condition.value, condition.operator)
    )
  },

  // 获取有效的查询条件
  validConditions() {
    return this.searchConditions.filter(condition =>
      condition.field && condition.operator && isValidConditionValue(condition.value, condition.operator)
    )
  },

  // 模块值
  MODULEValue() {
    return this.MODULE ?? (this.FUNID.includes('.') ? this.FUNID.split('.')[0] : 'admin')
  }
},
mounted() {
  this.initializeConditions()
},
watch: {

},
methods: {
  // 初始化条件列表
  async initializeConditions() {
    try {
      this.setLoading(true)
      const response = await initScheme({FUNID: this.FUNID}, this.MODULEValue)
      if(response && response.data){
        const data = response.data
        this.viewList = data.viewList || []
        this.currentView = data.viewList?.find(item => item.id === data.currentView)
        const condContent = data?.cond_content || []
        const searchContent = data?.search_content || []
        const sortContent = data?.sort_content || ""

        this.viewDataMap = {condContent, searchContent, sortContent}
        // 合并字段配置：接口数据 + 业务扩展配置
        this.fieldConfigs = this.mergeFieldConfigs(data.fieldConfigs, this.fieldConfigExtensions)

        // 处理动态日期操作符并加载当前视图
        const processedCondContent = SchemeDataUtils.processLoadedConditions(condContent)
        this.setConditions(processedCondContent)

        // 初始化完成后触发事件，传递方案数据
        this.$emit('initialized', {
          columnContent: this.fieldConfigs,
          condContent,
          searchContent,
          sortContent,
          currentView: this.currentView, // 传递当前方案信息
        })
      }
    } catch (error) {
      this.requestFailed(error)
      // 降级处理：使用默认数据
      this.setConditions([])
      // 即使失败也要触发初始化完成事件
      this.$emit('initialized', {
        columnContent: [],
        condContent: [],
        searchContent: [],
        sortContent: "",
        currentView: null,
      })
    }finally {
      this.setLoading(false)
    }
  },

  // 合并字段配置：根据 field 字段合并接口数据和业务扩展配置
  mergeFieldConfigs(apiConfigs = [], extensionConfigs) {
    apiConfigs = apiConfigs || []
    const merged = [...apiConfigs]

    // 遍历扩展配置，合并到对应的字段
    extensionConfigs.forEach(extension => {
      const existingIndex = merged.findIndex(config => config.field === extension.field)

      if (existingIndex >= 0) {
        // 字段已存在，合并配置（扩展配置优先级更高）
        merged[existingIndex] = {
          ...merged[existingIndex],
          ...extension
        }
        // console.log(`合并字段配置 ${extension.field}:`, merged[existingIndex])
      } else {
        // 字段不存在，直接添加
        merged.push(extension)
        // console.log(`添加新字段配置 ${extension.field}:`, extension)
      }
    })

    return merged
  },

  async selectScheme(scheme) {
    this.currentView = this.viewList.find(item => item.id === scheme)
    try{
      this.setLoading(true)
      const { data } = await getScheme({id: this.currentView.id, FUNID: this.FUNID }, this.MODULEValue)
      const columnContent = data?.column_content || []
      const condContent = data?.cond_content || []
      const searchContent = data?.search_content || []
      const sortContent = data?.sort_content || []
      this.viewDataMap = { condContent, searchContent, sortContent }

      // 处理动态日期操作符并设置条件
      const processedCondContent = SchemeDataUtils.processLoadedConditions(condContent)
      this.setConditions(processedCondContent)

      // 触发方案切换事件，传递列配置数据
      this.$emit('schemeChange', {
        columnContent,
        condContent,
        searchContent,
        sortContent,
        currentView: this.currentView // 传递当前方案信息
      })

      this.handleSearch()
    }catch (err){
      this.requestFailed(err)
    }finally {
      this.setLoading(false)
    }
  },

  setLoading(boolean){
    this.loading = boolean
  },

  toggleFilter() {
    this.showFilter = !this.showFilter
  },
  
  // 处理条件变更
  handleConditionChange(index, newCondition) {
    this.$set(this.searchConditions, index, newCondition)
  },
  
  // 删除条件
  handleConditionDelete(index) {
    this.searchConditions.splice(index, 1)
  },

  // 刷新数据
  handleRefresh(){
    this.initializeConditions()
  },

  // ========== Popover 操作按钮方法 ==========

  // 保存：将临时数据同步到实际数据
  async handleSave() {
    const params = {
      id: this.currentView.id,
      FUNID: this.FUNID,
      type: this.currentView.type,
      name: this.currentView.name,
      is_show: 'T',
      cond_content: JSON.parse(JSON.stringify(this.tempSearchConditions)).map(item => {
        const { label, component, ...itemWithoutLabel } = item
        itemWithoutLabel.fid = this.currentView.id

        // 处理动态日期操作符：保存时将 value 设为 null
        if (this.isDynamicDateOperator(itemWithoutLabel.operator)) {
          itemWithoutLabel.value = null
          console.log(`保存快速过滤时处理动态日期操作符 ${itemWithoutLabel.operator}，将 value 设为 null`)
        }

        return itemWithoutLabel
      })

    }
    try{
      await saveScheme(params,'mes')
      this.popoverVisible = false
      this.$message.success(this.$t('public.success'))
      await this.selectScheme(this.currentView.id)
    }catch (err){
      this.requestFailed(err)
    }
  },

  // 恢复：将实际数据恢复到临时数据
  handleRestore() {
    // 深拷贝实际数据到临时数据
    this.tempSearchConditions = JSON.parse(JSON.stringify(this.searchConditions))
    this.$message.success(this.$t('public.success'))
  },

  // 取消：关闭 popover，不保存临时修改
  handleCancel() {
    this.popoverVisible = false
    this.tempSearchConditions = []
  },

  // 显示方案管理弹窗
  showSchemeManager() {
    this.schemeDialogVisible = true
  },

  // ========== Popover 显示控制 ==========

  // 当 popover 显示时，初始化临时数据
  handlePopoverShow() {
    this.initTempData()
  },

  // 初始化临时数据（深拷贝实际数据）
  initTempData() {
    this.tempSearchConditions = JSON.parse(JSON.stringify(this.searchConditions))
    this.$emit('settings', this.tempSearchConditions)
  },

  // ========== 临时数据操作方法 ==========

  // 处理临时条件变更
  handleTempConditionChange(index, newCondition) {
    this.$set(this.tempSearchConditions, index, newCondition)
  },

  // 删除临时条件
  handleTempConditionDelete(index) {
    this.tempSearchConditions.splice(index, 1)
  },

  // 删除全部临时条件
  handleTempDelAllCondition(){
    this.tempSearchConditions = []
  },

  // 临时添加条件（在 popover 中使用）
  handleTempAddCondition(fieldValue) {
    console.log('临时添加条件:', fieldValue)
    if (!fieldValue) return

    // 检查是否已经存在该字段的条件
    const existingCondition = this.tempSearchConditions.find(condition =>
      condition.field === fieldValue
    )
    if (existingCondition) {
      this.$message.warning('该字段的查询条件已存在')
      return
    }

    // 检查条件数量限制
    const activeConditionsCount = this.tempSearchConditions.length
    if (activeConditionsCount >= this.maxConditions) {
      this.$message.warning(`最多只能添加${this.maxConditions}个查询条件`)
      return
    }

    // 获取字段配置
    const fieldConfig = this.fieldConfigs.find(f => f.field === fieldValue)
    if (!fieldConfig) {
      this.$message.error('字段配置不存在')
      return
    }

    // 确定组件类型
    const componentType = fieldConfig.component || 'input'

    // 创建新条件
    const newCondition = {
      field: fieldValue,
      operator: getDefaultOperator(componentType),
      value: fieldConfig.value !== undefined ? fieldConfig.value : null,
      component: componentType,
      label: fieldConfig.title || fieldConfig.label,
    }

    console.log(`临时添加条件 ${fieldValue}:`, {
      组件类型: componentType,
      默认操作符: newCondition.operator,
      初始值: newCondition.value,
      字段配置: fieldConfig
    })

    this.tempSearchConditions.push(newCondition)

    // 清空选择框
    this.$nextTick(() => {
      if (this.$refs.addConditionSelect) {
        this.$refs.addConditionSelect.blur()
      }
    })
    console.log('临时条件列表更新:', this.tempSearchConditions)
  },
  
  // 重置条件
  resetConditions() {
    this.initializeConditions()
    this.$emit('reset')
  },
  
  // 处理快捷查询事件
  handleSelectListEvent(eventData) {
    this.$emit('selectListEvent', eventData)
  },
  
  // 处理图标点击事件
  handleIconClick(eventData) {
    this.$emit('iconClick', eventData)
  },
  
  // 执行查询
  handleSearch() {
    const searchParams = this.getSearchParams()
    this.$emit('search', searchParams)
  },

  
  // 获取查询参数
  getSearchParams() {
    // 获取用户设置的有效条件
    const userConditions = this.validConditions.map(condition => {
      const fieldConfig = this.fieldConfigs.find(f => f.field === condition.field)
      return {
        field: condition.field,
        operator: condition.operator,
        value: formatFieldValue(condition.value, fieldConfig, condition.operator),
        groupid: condition.groupid || null,
      }
    })
    // 获取方案中的查询条件（search_content）
    const schemeConditions = this.getSchemeSearchConditions()

    // 合并条件：用户条件 + 方案条件
    const allConditions = this.mergeSearchConditions(userConditions, schemeConditions)

    return allConditions
  },

  // 获取方案中的查询条件（search_content）
  getSchemeSearchConditions() {
    if (!this.currentView || !this.viewDataMap || !this.viewDataMap.searchContent) {
      return []
    }

    const searchContent = this.viewDataMap.searchContent || []

    return searchContent
      .filter(condition => {
        // 过滤出有效的方案条件
        return condition.field && condition.operator && isValidConditionValue(condition.value, condition.operator)
      })
      .map(condition => {
        const fieldConfig = this.fieldConfigs.find(f => f.field === condition.field)
        return {
          field: condition.field,
          operator: condition.operator,
          value: formatFieldValue(condition.value, fieldConfig, condition.operator),
          groupid: condition.groupid || null,
        }
      })
  },

  // 合并查询条件：用户条件 + 方案条件
  mergeSearchConditions(userConditions, schemeConditions) {
    const mergedConditions = [...userConditions]

    // 遍历方案条件，添加不重复的条件
    schemeConditions.forEach(schemeCondition => {
      // 检查是否已存在相同字段和操作符的条件
      const existingCondition = mergedConditions.find(condition =>
        condition.field === schemeCondition.field &&
        condition.operator === schemeCondition.operator
      )

      if (!existingCondition) {
        // 不存在重复条件，添加方案条件
        mergedConditions.push(schemeCondition)
        console.log(`添加方案条件: ${schemeCondition.field} ${schemeCondition.operator} ${schemeCondition.value}`)
      } else {
        console.log(`跳过重复的方案条件: ${schemeCondition.field} ${schemeCondition.operator}`)
      }
    })

    return mergedConditions
  },


  // 设置条件
  setConditions(conditions) {
    conditions = conditions || []
    this.searchConditions = conditions.map((condition, index) => {
      // 获取字段配置
      const fieldConfig = this.fieldConfigs.find(f => f.field === condition.field)

      // 确定组件类型，默认为 input
      const componentType = fieldConfig?.component || condition.component || 'input'

      // 如果找到字段配置，验证并调整操作符
      let validOperator = condition.operator || ''
      if (componentType) {
        // 检查当前操作符是否适用于该字段类型
        const validOperators = this.getValidOperatorsForField(componentType)

        // 如果当前操作符不适用，使用默认操作符
        if (!validOperators.includes(validOperator)) {
          validOperator = getDefaultOperator(componentType)
          console.log(`字段 ${condition.field} 的操作符从 ${condition.operator} 调整为 ${validOperator}`)
        }
      }

      // 处理值，确保数字0不被转换为null
      let processedValue = condition.value !== undefined ? condition.value : null

      const result = {
        // id: condition.id || index + 1,
        field: condition.field || '',
        operator: validOperator,
        value: processedValue,
        component: componentType,
        label: fieldConfig?.title || fieldConfig?.label || condition.label
      }

      // console.log(`设置条件 ${condition.field}:`, {
      //   原始值: condition.value,
      //   处理后值: processedValue,
      //   组件类型: componentType,
      //   操作符: validOperator,
      //   字段配置: fieldConfig
      // })

      return result
    })

  },

  // 获取字段类型支持的操作符列表
  getValidOperatorsForField(fieldType) {
    const operators = getOperatorsByType(fieldType)
    return operators.map(op => op.value)
  },

  // 判断是否为动态日期操作符
  isDynamicDateOperator(operator) {
    const dynamicOperators = [
      'today', 'yesterday', 'thisWeek', 'lastWeek', 'last7Days',
      'thisMonth', 'lastMonth', 'last30Days', 'thisYear', 'lastYear'
    ]
    return dynamicOperators.includes(operator)
  },
},

}
</script>

<style scoped lang="less">
  .search-form {
    border-radius: 4px;
    padding-bottom: 2px;
    background-color: #fff;
    border-bottom: 2px solid rgba(0, 0, 0, .1);
  }
  
  .search-form-top {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    padding-top: 10px;
  }
  
  .query-scheme-title {
    min-width: 80px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: flex-start;
    justify-content: center;
  }
  
  .button-container {
    display: flex;
    gap: 8px;
    margin-right: 16px;
  }
  
  .expand-box {
    margin-left: auto;
    display: flex;
    align-items: center;
  }
  
  .filter-toggle {
      box-sizing: border-box;
      height: 100%;
      background-color: #f5f7fa;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
  }
  
  .filter-toggle:hover {
      background-color: #e6f8f3;
  }
  
  .filter-toggle i {
    margin-left: 4px;
  }
  
  .search-form-field-item {
    display: flex;
  }
  
  .quick-title {
    min-width: 80px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 8px;
    
  }
  
  .search-items {
    flex: 1;
  }
  
  .action-buttons {
    display: flex;
  }
  
  
  .step-checkbox {
    margin-top: 8px;
  }

  .setting-button {
    margin: 0 0 0 8px;
  }

  /* 方案管理按钮样式 */
  .scheme-manager-btn {
    margin-left: 10px;
    color: #606266;

    &:hover {
      color: #409EFF;
    }
  }

  .el-popover .condition-popover {
    .footer{
      display: flex; flex-direction: row;

      div:nth-child(1){
        flex: 1;
      }
      div:nth-child(2){
        display: flex;
      }
    }
  }
</style>
