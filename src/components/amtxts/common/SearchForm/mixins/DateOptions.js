
export const DateOptions = {
  // 构建时间范围
  /* 今日 00:00:00 ~ 当日 23:59:59.999 */
  today() {
    const start = new Date();
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setHours(23, 59, 59, 999);
    return [start, end];
  },

  /* 昨日 00:00:00 ~ 昨日 23:59:59.999 */
  yesterday() {
    const start = new Date();
    start.setDate(start.getDate() - 1);
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setHours(23, 59, 59, 999);
    return [start, end];
  },

  /* 本周：周一 00:00:00 ~ 周日 23:59:59.999 */
  thisWeek() {
    const now = new Date();
    const day = now.getDay() || 7; // 0 -> 7（周日）

    // 本周一
    const start = new Date(now);
    start.setDate(now.getDate() - (day - 1));
    start.setHours(0, 0, 0, 0);

    // 本周日
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    end.setHours(23, 59, 59, 999);
    return [start, end];
  },

  /* 上周：周一 00:00:00 ~ 周日 23:59:59.999 */
  lastWeek() {
    const [thisStart] = this.thisWeek();

    // 上周一
    const start = new Date(thisStart);
    start.setDate(thisStart.getDate() - 7);

    // 上周日
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    end.setHours(23, 59, 59, 999);
    return [start, end];
  },

  /* 最近 7 天（含今天）：7 天前 00:00:00 ~ 今天 23:59:59.999 */
  last7Days() {
    const end = new Date();
    end.setHours(23, 59, 59, 999);

    const start = new Date(end);
    start.setDate(end.getDate() - 6);
    start.setHours(0, 0, 0, 0);
    return [start, end];
  },

  /* 本月：1 号 00:00:00 ~ 最后一天 23:59:59.999 */
  thisMonth() {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    return [start, end];
  },

  /* 上月：1 号 00:00:00 ~ 最后一天 23:59:59.999 */
  lastMonth() {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth() - 1, 1, 0, 0, 0, 0);

    const end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
    return [start, end];
  },

  /* 最近 30 天（含今天）：30 天前 00:00:00 ~ 今天 23:59:59.999 */
  last30Days() {
    const end = new Date();
    end.setHours(23, 59, 59, 999);

    const start = new Date(end);
    start.setDate(end.getDate() - 29);
    start.setHours(0, 0, 0, 0);
    return [start, end];
  },

  /* 今年：1 月 1 日 00:00:00 ~ 12 月 31 日 23:59:59.999 */
  thisYear() {
    const year = new Date().getFullYear();
    const start = new Date(year, 0, 1, 0, 0, 0, 0);
    const end = new Date(year, 11, 31, 23, 59, 59, 999);
    return [start, end];
  },

  /* 去年：1 月 1 日 00:00:00 ~ 12 月 31 日 23:59:59.999 */
  lastYear() {
    const year = new Date().getFullYear() - 1;
    const start = new Date(year, 0, 1, 0, 0, 0, 0);
    const end = new Date(year, 11, 31, 23, 59, 59, 999);
    return [start, end];
  },
  shortcuts: [
    {
      text: '今天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.today())
      }
    },
    {
      text: '昨天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.yesterday())
      }
    },
    {
      text: '本周',
      onClick(picker) {
        picker.$emit('pick', DateOptions.thisWeek())
      }
    },
    {
      text: '上周',
      onClick(picker) {
        picker.$emit('pick', DateOptions.lastWeek())
      }
    },
    {
      text: '近7天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.last7Days())
      }
    },
    {
      text: '本月',
      onClick(picker) {
        picker.$emit('pick', DateOptions.thisMonth())
      }
    },
    {
      text: '上月',
      onClick(picker) {
        picker.$emit('pick', DateOptions.lastMonth())
      }
    },
    {
      text: '近30天',
      onClick(picker) {
        picker.$emit('pick', DateOptions.last30Days())
      }
    },
    {
      text: '今年',
      onClick(picker) {
        picker.$emit('pick', DateOptions.thisYear())
      }
    },
    {
      text: '去年',
      onClick(picker) {
        picker.$emit('pick', DateOptions.lastYear())
      }
    }


  ]

}